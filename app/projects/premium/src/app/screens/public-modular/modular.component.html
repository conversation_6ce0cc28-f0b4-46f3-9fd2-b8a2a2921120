@let screenSize = "md" | responsive | async;
@let screenIsMobile = screenSize === false;

<ng-template #grid let-data="data" let-layout="layout" let-title="title" let-button="button" let-isButtonVisible="isButtonVisible">
  @let numberOfTiles = (layout.top === "hero" ? 1 : layout.top || 0) + (layout.bottom || 0);
  <div>
    @if (screenIsMobile) {
      <app-card-carousel class="flex md:!hidden" [title]="title">
        @for (spec of data; let index = $index; track spec) {
          @if (index < numberOfTiles) {
            <ng-template
              cardCarouselSlide
              [image]="spec.image"
              [title]="spec.title"
              [text]="spec.text"
              [month]="spec.month"
              [year]="spec.year"
              [link]="['/p/girl/info', spec.id]"
            >
            </ng-template>
          }
        }
      </app-card-carousel>
    } @else {
      @let gridCols = (layout.top === 3 && layout.bottom === 4) || (layout.top === 4 && layout.top === 3)
        ? 12
        : (layout.bottom === 4 || layout.top === 4) ? 4 : (layout.bottom === 3 || layout.top === 3)
          ? 3
          : (layout.bottom === 2 || layout.top === 2) ? 2 : 1;
      @if (title) {
        <h2 class="mb-6 md:mb-10 py-4 w-full text-center">
          {{ title }}
        </h2>
      }
      <div
        class="grid gap-6"
        [class]="'grid-cols-' + gridCols"
      >
        @for (gallery of data; let index = $index; track index) {
          @if (index < numberOfTiles) {
            <a
              [routerLink]="['/p/girl/info', gallery.id]"
              class="flex h-full w-full"
              [class.w-full]="index === 0 && layout.top === 'hero'"
              [class.col-span-full]="index === 0 && layout.top === 'hero'"
              [class.col-span-3]="gridCols === 12 && layout.bottom === 4 && index >= 3"
              [class.col-span-4]="gridCols === 12 && layout.top === 3 && index < 3"
            >
              <app-gallery-card
                [image]="gallery.image"
                [title]="gallery.title"
                [month]="gallery.month"
                [year]="gallery.year"
                [focalPoint]="gallery.focalPoint"
                [name]="gallery.text"
                [isNew]="true"
                [imageRatio]="index === 0 && layout.top === 'hero' ? 2 : 1"
                [overwriteImageRatio]="
              index === 0 && layout.top === 'hero' ? '2 / 1' : null
            "
                class="w-full"
                [autoSize]="false"
              >
              </app-gallery-card>
            </a>
          }
        }
      </div>
    }
    @if (isButtonVisible && button?.url && button?.text) {
      <div class="flex px-3 md:px-0 mt-4 md:mt-10 justify-center">
        <a
          [routerLink]="button.url"
          class="uppercase font-inter text-xs lg:text-sm tracking-widest p-5 inline-flex border border-white rounded-lg cursor-pointer justify-center w-max"
        >
          {{ button.text }}
        </a>
      </div>
    }
  </div>
</ng-template>

<div class="flex flex-col gap-16 py-16 lg:gap-20 lg:py-20">
  @if ($modules | async; as modules) {
    @for (module of modules; track module) {
      @if (module.type === 'category_module' && module.categories) {
        <div>
          @if (module.title) {
            <h2 class="mb-6 md:mb-10 py-4 w-full text-center">{{ module.title }}</h2>
          }
          <div class="grid gap-3 lg:gap-6 grid-cols-1 lg:grid-cols-4">
            @for (cat of module.categories; track cat.id) {
              <a
                [routerLink]="cat.link"
                class="flex h-full w-full"
                style="aspect-ratio: 1.5 / 1"
              >
                <app-category-tile
                  [image]="cat.image"
                  [tileTitle]="cat.title"
                  [focalPoint]="cat.focalPoint"
                ></app-category-tile>
              </a>
            }
          </div>
        </div>
      }
      @if (module.type === 'e_paper_archiv_module' && module.magazines) {
        <div>
          @if (module.title) {
            <h2 class="mb-6 md:mb-10 py-4 w-full text-center">{{ module.title }}</h2>
          }
          <div
            class="grid gap-6 md:gap-8 grid-cols-3 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-6"
          >
            @for (mag of module.magazines; track mag.url) {
              <a [href]="mag.url" target="_blank" class="flex h-full w-full">
                <app-article-preview
                  class="rounded-lg overflow-hidden"
                  style="aspect-ratio: 1 / 1.27"
                  [image]="mag.field_media_image || mag.field_archiv_image"
                ></app-article-preview>
              </a>
            }
          </div>
          @if (module.isButtonVisible && module.button.url && module.button.text) {
            <div class="flex px-3 md:px-0 mt-6 md:mt-10 justify-center">
              <a
                class="uppercase font-inter text-xs lg:text-sm tracking-widest p-5 inline-flex border border-white rounded-lg cursor-pointer justify-center w-max"
                [href]="module.button.url"
                target="_blank"
              >
                {{ module.button.text }}
              </a>
            </div>
          }
        </div>
      }
      @if (module.type === 'feature_module') {
        <ng-container
          *ngTemplateOutlet="
          grid;
          context: {
            title: module.title,
            isButtonVisible: module.isButtonVisible,
            button: {
              url: module.button.url,
              text: module.button.text,
            },
            data: module.girls,
            layout: module.gridLayout,
          }
        "
        >
        </ng-container>
      }
      @if (module.type === 'search_module') {
        <div class="py-20">
          @if (module.title) {
            <h2 class="mb-6 md:mb-10 py-4 w-full text-center">{{ module.title }}</h2>
          }
          <div class="flex flex-col items-center relative mx-auto justify-center">
            <div class="w-full xl:w-1/2 relative z-30">
              <app-search-input
                (triggerSearch)="goToSearch($event)"
                [autofocus]="false"
                [externalMode]="true"
                placeholder="Suche"
              ></app-search-input>
            </div>
          </div>
        </div>
      }
    }
  }
</div>
