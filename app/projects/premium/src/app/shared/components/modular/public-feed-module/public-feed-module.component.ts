import { Component, Input, OnDestroy } from '@angular/core';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { IPreview } from 'projects/premium/src/app/models/preview';
import { combineLatest, Observable, Subscription } from 'rxjs';
import { debounceTime, distinctUntilChanged, map } from 'rxjs/operators';

import { AsyncPipe } from '@angular/common';
import { Meta, Title } from '@angular/platform-browser';

import { PublicService } from 'projects/premium/src/app/services/public.service';
import { PaginationGridComponent } from 'projects/premium/src/app/shared';
import { GalleryCardComponent } from '../../../../shared/components/gallery-card/gallery-card.component';

@Component({
  selector: 'public-feed-module',
  templateUrl: './public-feed-module.component.html',
  imports: [
    AsyncPipe,
    RouterLink,
    GalleryCardComponent,
    PaginationGridComponent,
    ReactiveFormsModule,
  ],
})
export class PublicFeedModuleComponent implements OnDestroy {
  @Input() title: string;

  $pageAmount: Observable<number>;
  models: IPreview[];
  loading = true;
  $page: Observable<number>;
  term: String;

  searchInput = new UntypedFormControl('', Validators.required);
  formGroup = new UntypedFormGroup({
    search: this.searchInput,
  });

  private inputSub: Subscription;
  private gallerySub: Subscription;

  constructor(
    private publicData: PublicService,
    private route: ActivatedRoute,
    private meta: Meta,
    public readonly router: Router,
    titleService: Title,
  ) {
    // set page title
    titleService.setTitle('Search | Playboy All Access');

    // handle page parameter from the URL
    this.$page = this.route.queryParamMap.pipe(
      map((v) => parseInt(v.get('page'))),
      map((v) => (isNaN(v) ? 0 : v)),
    );

    // set form value based on URL initially
    this.searchInput.setValue((this.route.snapshot.queryParams as any).query, {
      onlySelf: true,
    });

    // set initial term based on URL initially
    this.term = (this.route.snapshot.queryParams as any).query;

    // subscription to listen for input changes
    this.inputSub = this.searchInput.valueChanges
      .pipe(distinctUntilChanged(), debounceTime(500))
      .subscribe((searchTerm) => {
        // set input search
        this.term = searchTerm;
        // update URL to entered search term
        return this.router.navigate([], { queryParams: { query: searchTerm } });
      });

    // react on changes for page and
    combineLatest([this.route.queryParamMap, this.$page])
      .pipe(distinctUntilChanged())
      .subscribe((e) => {
        // turn on loading immediately on change
        this.loading = true;

        if (!!this.term) {
          // load gallery
          this.gallerySub = this.publicData
            .getGirlInfos(e[1], null, this.term)
            .subscribe((data) => {
              this.models = data.map((model) => {
                return {
                  ...model,
                  image: model.imageLowRes,
                };
              });

              // turn off loading
              this.loading = false;

              // unsubscribe the sub again
              this.gallerySub.unsubscribe();
            });
        } else {
          // turn off loading for fallback
          this.loading = false;
        }

        // load total galleries count
        this.$pageAmount = this.publicData.getGirlInfosCount();
      });
  }

  ngOnDestroy() {
    this.inputSub?.unsubscribe();
    this.gallerySub?.unsubscribe();
  }
}
