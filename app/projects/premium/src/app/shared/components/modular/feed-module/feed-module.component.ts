import {
  animate,
  keyframes,
  style,
  transition,
  trigger,
} from '@angular/animations';
import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { distinctJSON } from '@pb/ui';
import { IFilterOptions } from '@pb/ui/components/filter-dropdown/filter-dropdown.component';
import { $ShownPerScreen } from '@pb/ui/utils/screen';
import { IPreview } from '../../../../models/preview';
import { BehaviorSubject, combineLatest, Observable, Subscription } from 'rxjs';
import {
  debounceTime,
  distinctUntilChanged,
  filter,
  map,
  skip,
} from 'rxjs/operators';
import { CategoriesService } from '../../../../services/categories/categories.service';

import {
  GirlInfoListQueryParams,
  GirlInfoService,
  ICategoryFilterOptions,
  IGirlInfoServiceData,
  SortOptions,
} from '../../../../services/girl-info/girl-info.service';
import { ICategoryQueryVariableSort } from '../../../../services/girl-info/models';
import {
  GenerateMainQueryParams,
  IGirlInfoPreviewByDescriptionIdQueryParams,
} from '../../../../services/girl-info/queries';
import { AsyncPipe } from '@angular/common';
import { GalleryCardComponent, PaginationGridComponent } from '../../../index';

@Component({
  selector: 'feed-module',
  templateUrl: './feed-module.component.html',
  imports: [
    AsyncPipe,
    RouterLink,
    GalleryCardComponent,
    PaginationGridComponent,
  ],
})
export class FeedModuleComponent implements OnInit, OnDestroy {
  @Input() title: string;
  // $queryResult: Observable<IGirlInfoServiceData>;
  private _queryResultSubject = new BehaviorSubject<IGirlInfoServiceData>(null);
  private loadingSubject = new BehaviorSubject(true);

  $girls: Observable<IPreview[]> = this._queryResultSubject.pipe(
    map(
      (v) =>
        v?.data.map(({ image, imageLowRes, ...preview }) => ({
          ...preview,
          image: imageLowRes,
        })) || [],
    ),
    distinctJSON(),
  );

  $headerImage: Observable<IPreview | undefined> = this.$girls.pipe(
    map((v) => v[0]),
    distinctJSON(),
  );

  term: String;
  get Loading(): boolean {
    return this.loadingSubject.getValue();
  }
  set Loading(loading: boolean) {
    this.loadingSubject.next(loading);
  }
  $loading = this.loadingSubject;

  $pageAmount = combineLatest([$ShownPerScreen, this._queryResultSubject]).pipe(
    filter(([_, v]) => !!v),
    map(([perScreen, { count }]) => Math.ceil(count / perScreen)),
    distinctUntilChanged((a, b) => a === b),
  );
  $page: Observable<number>;

  private selectedSorting = new BehaviorSubject<SortOptions | undefined>(
    undefined,
  );
  private selectedFilter = new BehaviorSubject<Partial<ICategoryFilterOptions>>(
    {},
  );

  private $activeSorting: Observable<ICategoryQueryVariableSort>;
  public $selectedSorting = this.selectedSorting.pipe(distinctJSON());
  public $selectedFilter = this.selectedFilter.pipe(distinctJSON());

  sortingOptions: SortOptions[] = [];
  $filterOptions: Observable<IFilterOptions>;

  private sub: Subscription;
  private loadingSub: Subscription;
  private girlsSub: Subscription;
  private paramsSub: Subscription;

  public set Page(page: number) {
    this.router.navigate([], {
      queryParams: { page },
      queryParamsHandling: 'merge',
    });
  }

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private girlInfo: GirlInfoService,
    categories: CategoriesService,
  ) {
    this.$page = route.queryParamMap.pipe(
      map((v) => parseInt(v.get('page'))),
      map((v) => (isNaN(v) ? 0 : v)),
    );
    this.term = (route.snapshot.queryParams as any).query;

    this.$filterOptions = combineLatest([
      girlInfo.$filterOptions,
      categories.$filterOptions,
    ]).pipe(
      map(([defaultFilters, categoryFilters]) => ({
        ...defaultFilters,
        ...categoryFilters,
      })),
    );
    this.$activeSorting = girlInfo.mapSortingObservable(this.$selectedSorting);
    this.sortingOptions = girlInfo.sortingOptions;
  }

  ngOnInit() {
    const $filterParams = combineLatest([
      this.$selectedFilter,
      this.$activeSorting,
    ]).pipe(
      distinctJSON(),
      map((d) => GenerateMainQueryParams(...d)),
      distinctJSON(),
    );
    const $params = combineLatest([
      $ShownPerScreen,
      this.$page,
      this.route.queryParamMap.pipe(
        map((v) => v.get('query')),
        distinctUntilChanged((a, b) => a === b),
      ),
      $filterParams,
    ]).pipe(
      debounceTime(300),
      distinctJSON(),
      map(
        ([perScreen, screen, searchName, params]): [
          GirlInfoListQueryParams,
          Partial<IGirlInfoPreviewByDescriptionIdQueryParams>,
        ] => [
          { perScreen, screen },
          { ...params, searchName },
        ],
      ),
    );
    this.girlsSub = this.girlInfo
      .getGirlInfoPreviews($params)
      .subscribe((data) => this._queryResultSubject.next(data));

    this.loadingSub = this._queryResultSubject.subscribe(
      (d) => (this.Loading = !!d?.loading),
    );

    this.paramsSub = $filterParams
      .pipe(skip(1))
      .subscribe(() => (this.Page = 0));
  }

  ngOnDestroy(): void {
    this.loadingSub?.unsubscribe();
    this.girlsSub?.unsubscribe();
    this.paramsSub?.unsubscribe();
  }
}
