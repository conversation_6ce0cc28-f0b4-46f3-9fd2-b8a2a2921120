import { computed, Injectable, Input } from '@angular/core';
import { Apollo } from 'apollo-angular';
import { combineLatest, Observable, of, forkJoin } from 'rxjs';
import { map, switchMap, tap } from "rxjs/operators";
import {
  GRAPHQL_GIRL_INFOS_BY_CATEGORY_QUERY,
  GRAPHQL_POPULAR_GIRL_INFOS_BY_CATEGORY_QUERY,
  MODULAR_PAGE_QUERY,
  GRAPHQL_CATEGORY_BY_ID_GRAPHQL_1_VIEW,
} from './queries';
import { VIDEOS_QUERY } from '../../screens/videos/definitions/queries';
import { GIRLS_OF_THE_DAY_QUERY } from '../../screens/landing/subscribed/definitions/queries';
import { IVideo } from '../../screens/videos/definitions/models';
import { IGDTData } from '../../screens/landing/subscribed/definitions/interfaces';
import { Magazine, MagazineService } from '../magazine/magazine.service';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { ApiService } from '../api.service';
import { PublicGirlInfo, PublicService } from '../public.service';
import { parseNumberOrNull } from '../../utils/parseNumberOrNull';
import { GetFocalPoint } from '../../shared/pipes/focal-point.pipe';

// grid-layout
// 0|Hero-Grid
// 1|Hero/2-Grid
// 2|Hero/3-Grid
// 3|Hero/4-Grid
// 4|2/2-Grid
// 5|3/3-Grid
// 6|4/4-Grid
// 7|2-Grid
// 8|3-Grid
// 9|4-Grid

// sorting
// 0|by date
// 1|by popularity

// grid-layout-2
// 0|4 Grid
// 1|4-4 Grid

// grid_layout_3
// 0|4 Grid
// 1|4-4 Grid
// 2|3 Grid
// 3|3-3 Grid
// 4|3-4 Grid

const fieldGridLayoutRecord: Record<
  number,
  {
    top: 'hero' | 2 | 3 | 4;
    bottom?: 2 | 3 | 4;
  }
> = {
  0: { top: 'hero' },
  1: { top: 'hero', bottom: 2 },
  2: { top: 'hero', bottom: 3 },
  3: { top: 'hero', bottom: 4 },
  4: { top: 2, bottom: 2 },
  5: { top: 3, bottom: 3 },
  6: { top: 4, bottom: 4 },
  7: { top: 2 },
  8: { top: 3 },
  9: { top: 4 },
};

const fieldGridLayout2Record: Record<
  number,
  {
    top: 4;
    bottom?: 4;
  }
> = {
  0: { top: 4 },
  1: { top: 4, bottom: 4 },
};

const fieldGridLayout3Record: Record<
  number,
  {
    top: 3 | 4;
    bottom?: 3 | 4;
  }
> = {
  0: { top: 4 },
  1: { top: 4, bottom: 4 },
  2: { top: 3 },
  3: { top: 3, bottom: 3 },
  4: { top: 3, bottom: 4 },
};

const gridLayoutRecord: {
  fieldGridLayout: typeof fieldGridLayoutRecord;
  fieldGridLayout2: typeof fieldGridLayout2Record;
  fieldGridLayout3: typeof fieldGridLayout3Record;
} = {
  fieldGridLayout: fieldGridLayoutRecord,
  fieldGridLayout2: fieldGridLayout2Record,
  fieldGridLayout3: fieldGridLayout3Record,
};

function getGridLayout(
  options:
    | { fieldGridLayout: keyof typeof fieldGridLayoutRecord }
    | { fieldGridLayout2: keyof typeof fieldGridLayout2Record }
    | { fieldGridLayout3: keyof typeof fieldGridLayout3Record },
): {
  top: 'hero' | 2 | 3 | 4;
  bottom?: 2 | 3 | 4;
} {
  if ('fieldGridLayout' in options) {
    return gridLayoutRecord.fieldGridLayout[options.fieldGridLayout];
  }

  if ('fieldGridLayout2' in options) {
    return gridLayoutRecord.fieldGridLayout2[options.fieldGridLayout2];
  }

  if ('fieldGridLayout3' in options) {
    return gridLayoutRecord.fieldGridLayout3[options.fieldGridLayout3];
  }
}

@Injectable({ providedIn: 'root' })
export class ModularService {
  constructor(
      private apollo: Apollo,
      private http: HttpClient,
      private magazineService: MagazineService,
      private api: ApiService,
      private publicData: PublicService,
  ) {}

  getSubscribedModules({ url }: { url: string }): Observable<{ modules: any; title?: string }> {
    return this.apollo
      .query<any>({
        query: MODULAR_PAGE_QUERY,
        variables: { url },
      })
      .pipe(
        map((res) => {
          const pageData = res.data.modularPageGraphql1View.results?.[0];
          return {
            modules: pageData?.fieldModules || [],
            title: pageData?.entityLabel
          };
        }),
        switchMap(({ modules, title }) => {
          const enrichedModule$ = combineLatest(
            modules.map((module) => {
              // category_module
              if (module.entity.entityBundle === 'category_module') {
                const gridLayout = getGridLayout({
                  fieldGridLayout2: module.entity.fieldGridLayout2,
                });

                if (module.entity.fieldCategories && module.entity.fieldCategories.length > 0) {
                  const categoryQueries = module.entity.fieldCategories.map(category =>
                    this.apollo.query<any>({
                      query: GRAPHQL_CATEGORY_BY_ID_GRAPHQL_1_VIEW,
                      variables: {
                        url: category.targetId.toString(),
                      },
                    })
                  );

                  return forkJoin(categoryQueries).pipe(
                    map((responses: any[]) => {
                      const allCategories = responses.flatMap(res =>
                        res.data.graphqlCategoryByIdGraphql1View.results.map(category => {
                          return {
                            id: category.entityLabel,
                            link: decodeURIComponent(category.fieldLink?.url?.path || ''),
                            image: category.fieldImage?.entity?.fieldMediaImage?.url ||
                                   category.fieldImage?.entity?.fieldMediaImage1?.url,
                            focalPoint: category.fieldImage?.entity
                              ? GetFocalPoint(category.fieldImage.entity as any)
                              : undefined,
                            tileTitle: category.entityLabel
                          };
                        })
                      );

                      return {
                        ...module,
                        title: module.entity.fieldTitle,
                        isButtonVisible: module.entity.fieldButtonBool,
                        button: {
                          url: module.entity.fieldButton?.url?.path,
                          text: module.entity.fieldButton?.title,
                        },
                        categories: allCategories,
                        gridLayout,
                      };
                    })
                  );
                }
              }
              // feature_module
              if (module.entity.entityBundle === 'feature_module') {
                const gridLayout = getGridLayout({
                  fieldGridLayout: module.entity.fieldGridLayout,
                });
                const pageSize =
                  (gridLayout.top === 'hero' ? 1 : gridLayout.top) +
                  (gridLayout.bottom || 0);
                if (
                  module.entity.fieldCategoryBool === true &&
                  module.entity.fieldCategoryReference?.length > 0
                ) {
                  const country = module.entity.fieldDescriptorCountry?.targetId;
                  const categories = module.entity.fieldCategoryReference?.map((c) => c.targetId).join(',');
                  const popular = module.entity.fieldSorting === 1;
                  return this.apollo
                    .query<any>({
                      query:
                        popular
                          ? GRAPHQL_POPULAR_GIRL_INFOS_BY_CATEGORY_QUERY
                          : GRAPHQL_GIRL_INFOS_BY_CATEGORY_QUERY,
                      variables: {
                        url: categories,
                        urlTwo: country,
                      },
                    })
                    .pipe(
                      map((res) => {
                        const results =
                          popular
                            ? res.data?.graphqlPopularGirlInfosByCategoryGraphql1View?.results
                            : res.data?.graphqlGirlInfosByCategoryGraphql1View?.results;
                        const girls = results?.map((result) => ({
                          reverseGalleriesGirlInfo: { entities: [result] },
                        }));
                        return {
                          ...module,
                          title: module.entity.fieldTitle,
                          isButtonVisible: module.entity.fieldButtonBool,
                          button: {
                            url: module.entity.fieldButton?.url?.path,
                            text: module.entity.fieldButton?.title,
                          },
                          girls,
                          gridLayout,
                        };
                      }),
                    );
                } else {
                  const girls = module.entity.fieldGirlInfo.map(
                    ({ entity, ...rest }) => ({
                      reverseGalleriesGirlInfo: { entities: [entity] },
                      ...rest,
                    }),
                  );

                  return of({
                    ...module,
                    title: module.entity.fieldTitle,
                    isButtonVisible: module.entity.fieldButtonBool,
                    button: {
                      url: module.entity.fieldButton?.url?.path,
                      text: module.entity.fieldButton?.title,
                    },
                    girls,
                    gridLayout,
                  });
                }
              }

              // girl_des_tages_module
              if (module.entity.entityBundle === 'girl_des_tages_module') {
                const gridLayout = getGridLayout({
                  fieldGridLayout3: module.entity.fieldGridLayout3,
                });
                const pageSize =
                  (gridLayout.top === 'hero' ? 1 : gridLayout.top) +
                  (gridLayout.bottom || 0);
                return this.apollo
                  .query<IGDTData>({
                    query: GIRLS_OF_THE_DAY_QUERY,
                    variables: {
                      sortBy: 'FIELD_PUBLISH_DATE_VALUE',
                      pageSize,
                      sortDirection: 'DESC',
                      page: 0,
                    },
                  })
                  .pipe(
                    map((res) => {
                      const girlsOfTheDay =
                        res.data.pbGalerieDesTages.results.flatMap(
                          ({ fieldGirlInfos, ...rest }) =>
                            fieldGirlInfos.map(({ entity }) => ({
                              reverseGalleriesGirlInfo: { entities: [entity] },
                              ...rest,
                            })),
                        );
                      return {
                        ...module,
                        title: module.entity.fieldTitle,
                        isButtonVisible: module.entity.fieldButtonBool,
                        button: {
                          url: module.entity.fieldButton?.url?.path,
                          text: module.entity.fieldButton?.title,
                        },
                        girlsOfTheDay,
                        gridLayout,
                      };
                    }),
                  );
              }

              // video_module
              if (module.entity.entityBundle === 'video_module') {
                const gridLayout = getGridLayout({
                  fieldGridLayout2: module.entity.fieldGridLayout2,
                });
                const pageSize =
                  (gridLayout.top === 'hero' ? 1 : gridLayout.top) +
                  (gridLayout.bottom || 0);
                return this.apollo
                  .query<{
                    pbInfoWithLastVideoGallery: {
                      results: { fieldVideos: { entity: IVideo }[] }[];
                    };
                  }>({
                    query: VIDEOS_QUERY,
                    variables: {
                      pageSize,
                      page: 0,
                    },
                  })
                  .pipe(
                    map((res) => ({
                      ...module,
                      title: module.entity.fieldTitle,
                      videos:
                        res.data.pbInfoWithLastVideoGallery.results.flatMap(
                          (r: any) => r.fieldVideos.map((v: any) => v.entity),
                        ),
                      gridLayout,
                      maxNumberOfTiles: pageSize,
                    })),
                  );
              }

              // e_paper_archiv_module
              if (module.entity.entityBundle === 'e_paper_archiv_module') {
                const headers = new HttpHeaders({
                  Accept: 'application/json',
                });
                if (module.entity.fieldLoadNewest) {
                  const apiUrl = 'https://www.playboy.de/api/v1/issues';
                  return this.http.get<Magazine[]>(apiUrl, { headers }).pipe(
                    map((res) => {
                      const limitedMagazines = res.slice(0, 12);
                      return {
                        ...module,
                        title: module.entity.fieldTitle,
                        isButtonVisible: module.entity.fieldButtonBool,
                        button: {
                          url: module.entity.fieldButton?.url?.path,
                          text: module.entity.fieldButton?.title,
                        },
                        magazines: limitedMagazines,
                      };
                    }),
                  );
                } else {
                  const ids = module.entity.fieldMagazine.map((m: string) => {
                    const matches = [...m.matchAll(/\((\d+)\)/g)];
                    if (matches.length > 0) {
                      const id = matches[matches.length - 1][1];
                      return id;
                    }
                  });
                  if (!!ids.length) {
                    const joinedIds = ids.join('|');
                    const encoded = encodeURIComponent(`^(${joinedIds})$`);
                    const apiUrl = `https://www.playboy.de/api/v1/issues/single?nid=${encoded}`;
                    return this.http
                      .get<
                        Magazine[]
                      >(apiUrl, { headers })
                      .pipe(
                        map((res) => {
                          return {
                            ...module,
                            title: module.entity.fieldTitle,
                            isButtonVisible: module.entity.fieldButtonBool,
                            button: {
                              url: module.entity.fieldButton?.url?.path,
                              text: module.entity.fieldButton?.title,
                            },
                            magazines: res,
                          };
                        }),
                      );
                  }
                }
              }

              // favorites_module
              if (module.entity.entityBundle === 'favorites_module') {
                const gridLayout = getGridLayout({
                  fieldGridLayout2: module.entity.fieldGridLayout2,
                });
                const pageSize =
                  (gridLayout.top === 'hero' ? 1 : gridLayout.top) +
                  (gridLayout.bottom || 0);
                const isAllVisible = module.entity.fieldCategoryList === 3;
                const isGirlsVisible = isAllVisible || module.entity.fieldCategoryList === 0;
                const isGirlInfosVisible = isAllVisible || module.entity.fieldCategoryList === 1;
                const isVideosVisible = isAllVisible || module.entity.fieldCategoryList === 2;
                return of({
                  ...module,
                  title: module.entity.fieldTitle,
                  isGirlsVisible,
                  isGirlInfosVisible,
                  isVideosVisible,
                  pageSize
                });
              }

              // feed_module
              if (module.entity.entityBundle === 'feed_module') {
                return of({
                  title: module.entity.fieldTitle,
                  ...module,
                });
              }

              // search_module
              if (module.entity.entityBundle === 'search_module') {
                return of({
                  title: module.entity.fieldTitle,
                  ...module,
                });
              }

              return of(module);
            }),
          );

          return enrichedModule$.pipe(
            map((enrichedModules) => ({
              modules: enrichedModules,
              title,
            })),
          );
        }),
      );
  }
  getPublicModules({ url }: { url: string }): Observable<{ modules: any }> {
    return this.api.get(`public/modular/${url}`).pipe(
        switchMap((modules: any[]) => {
          const enrichedModule$ = combineLatest(
            modules.map((module) => {
              // category_module
              if (module.type === 'category_module') {
                const gridLayout = getGridLayout({
                  fieldGridLayout2: module.field_grid_layout_2,
                });

                if (module.field_categories && module.field_categories.length > 0) {
                  const categoryQueries = module.field_categories.map(category => this.api.get(`public/category-by-id/${category}`));
                  return forkJoin(categoryQueries).pipe(
                    map((responses: any[]) => {
                      const allCategories = responses.flatMap(res =>
                        res.map(category => {
                          return {
                            id: category.name,
                            link: decodeURIComponent(category.field_link || ''),
                            image:
                              category.field_media_image_1?.trim() ||
                              category.field_media_image?.trim(),
                            title: category.name,
                            focalPoint:
                              category.field_focal_point_x &&
                              category.field_focal_point_y
                                ? {
                                    x: parseInt(category.field_focal_point_x),
                                    y: parseInt(category.field_focal_point_y),
                                  }
                                : undefined,
                          };
                        })
                      );

                      return {
                        ...module,
                        title: module.field_title,
                        isButtonVisible: module.field_button_bool,
                        button: {
                          url: module.field_button_url,
                          text: module.field_button_text,
                        },
                        categories: allCategories,
                        gridLayout,
                      };
                    })
                  );
                }
              }
              // feature_module
              if (module.type === 'feature_module') {
                const gridLayout = getGridLayout({
                  fieldGridLayout: module.field_grid_layout,
                });
                const pageSize =
                  (gridLayout.top === 'hero' ? 1 : gridLayout.top) +
                  (gridLayout.bottom || 0);
                if (
                  module.field_category_bool === '1' &&
                  module.field_category_reference
                ) {
                  const categories = module.field_category_reference;
                  const country = module.field_descriptor_country;
                  const order = module.field_sorting === '1' ? 'popular' : 'by-date';
                  return this.publicData.getGirlInfos(0, categories, null, order, country)
                    .pipe(
                      map((res: any[]) => {
                        const girls = res;
                        return {
                          ...module,
                          title: module.field_title,
                          isButtonVisible: module.field_button_bool,
                          button: {
                            url: module.field_button_url,
                            text: module.field_button_text,
                          },
                          girls,
                          gridLayout,
                        };
                      }),
                    );
                } else {
                  const girlInfoQueries = module.field_girl_info.map(girlInfoId =>
                    this.publicData.getGirlInfo(girlInfoId)
                  );
                  return forkJoin(girlInfoQueries).pipe(
                    map((publicGirlInfos: PublicGirlInfo[]) => {
                      const allPublicGirlInfos = publicGirlInfos.map((item) => {
                        return {
                          id: item.girl_id,
                          text: item.name
                            .replaceAll('&quot;', '"')
                            .replaceAll('&amp;', '&')
                            .replaceAll('&#039;', "'"),
                          meta: {
                            images:
                              parseNumberOrNull(item.field_image_count) || 0,
                            videos:
                              parseNumberOrNull(item.field_video_count) || 0,
                            girlInfos: 0,
                          },
                          title: item.field_category,
                          image: item.images?.[0],
                          link: ['/p/girl/info', item.girl_id],
                          month: item.descriptor_month,
                          year: item.descriptor_year,
                          focalPoint: {
                            x: parseInt(item.field_main_focal_point_x),
                            y: parseInt(item.field_main_focal_point_y),
                          },
                        };
                      });

                      return {
                        ...module,
                        title: module.field_title,
                        isButtonVisible: module.field_button_bool,
                        button: {
                          url: module.field_button_url,
                          text: module.field_button_text,
                        },
                        girls: allPublicGirlInfos,
                        gridLayout,
                      };
                    }),
                  );
                }
              }

              // girl_des_tages_module
              if (module.type === 'girl_des_tages_module') {
                const gridLayout = getGridLayout({
                  fieldGridLayout3: module.field_grid_layout_3,
                });
                const pageSize =
                  (gridLayout.top === 'hero' ? 1 : gridLayout.top) +
                  (gridLayout.bottom || 0);
                return this.publicData.getGalleryOfDay()
                  .pipe(
                    map((res) => {
                      const girls = res;
                      return {
                        ...module,
                        title: module.field_title,
                        isButtonVisible: module.field_button_bool,
                        button: {
                          url: module.field_button_url,
                          text: module.field_button_text,
                        },
                        girls,
                        gridLayout,
                      };
                    }),
                  );
              }

              // e_paper_archiv_module
              if (module.type === 'e_paper_archiv_module') {
                const headers = new HttpHeaders({
                  Accept: 'application/json',
                });
                if (module.field_load_newest === '1') {
                  const apiUrl = 'https://www.playboy.de/api/v1/issues';
                  return this.http.get<Magazine[]>(apiUrl, { headers }).pipe(
                    map((res) => {
                      const limitedMagazines = res.slice(0, 12);
                      return {
                        ...module,
                        title: module.field_title,
                        isButtonVisible: module.field_button_bool,
                        button: {
                          url: module.field_button_url,
                          text: module.field_button_text,
                        },
                        magazines: limitedMagazines,
                      };
                    }),
                  );
                } else {
                  const ids = module.field_magazine.map((m: string) => {
                    const matches = [...m.matchAll(/\((\d+)\)/g)];

                    if (matches.length > 0) {
                      const id = matches[matches.length - 1][1];
                      return id;
                    }
                  });
                  if (!!ids.length) {
                    const joinedIds = ids.join('|');
                    const encoded = encodeURIComponent(`^(${joinedIds})$`);
                    const apiUrl = `https://www.playboy.de/api/v1/issues/single?nid=${encoded}`;
                    return this.http.get<Magazine[]>(apiUrl, {headers}).pipe(
                      map((res) => {
                        return {
                          ...module,
                          title: module.field_title,
                          isButtonVisible: module.field_button_bool,
                          button: {
                            url: module.field_button_url,
                            text: module.field_button_text,
                          },
                          magazines: res,
                        };
                      }),
                    );
                  }
                }
              }

              // feed_module
              if (module.type === 'feed_module') {
                return of({
                  ...module,
                  title: module.field_title,
                });
              }

              // search_module
              if (module.type === 'search_module') {
                return of({
                  ...module,
                  title: module.field_title,
                });
              }

              return of(module);
            }),
          );

          return enrichedModule$.pipe(
            // tap((enrichedModules) => console.log('Enriched modules:', enrichedModules)),
            map((enrichedModules) => ({
              modules: enrichedModules,
            })),
          );
        }),
      );
  }

}
